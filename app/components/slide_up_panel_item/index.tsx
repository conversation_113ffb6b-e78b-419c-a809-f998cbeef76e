// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {   type ImageSource, type ImageStyle } from 'expo-image';
import React, { useCallback, useEffect, useState } from 'react';
import { type StyleProp, Text, type TextStyle, TouchableHighlight, View, type ViewStyle,Image } from 'react-native';

import CompassIcon from '@components/compass_icon';
import { useTheme } from '@context/theme';
import { preventDoubleTap } from '@utils/tap';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';
import { typography } from '@utils/typography';
import { isValidUrl } from '@utils/url';




import * as RNLocalize from 'react-native-localize';
import { DEFAULT_LOCALE } from '@app/i18n';
import { ChatBubbleLeftRightIcon } from 'react-native-heroicons/outline';

type SlideUpPanelProps = {
    destructive?: boolean;
    leftIcon?: string | ImageSource;
    leftImageStyles?: StyleProp<ImageStyle>;
    leftIconStyles?: StyleProp<TextStyle>;
    rightIcon?: string | ImageSource;
    rightImageStyles?: StyleProp<ImageStyle>;
    rightIconStyles?: StyleProp<TextStyle>;
    onPress: () => void;
    textStyles?: TextStyle;
    testID?: string;
    text: string;
}

export const ITEM_HEIGHT = 48;

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => {
    return {
        container: {
            height: ITEM_HEIGHT,
            marginHorizontal: -20,
            paddingHorizontal: 20,
        },
        destructive: {
            color: theme.dndIndicator,
        },
        row: {
            width: '100%',
            flexDirection: 'row',
        },
        iconContainer: {
            height: ITEM_HEIGHT,
            justifyContent: 'center',
            marginRight: 10,
        },
        noIconContainer: {
            height: ITEM_HEIGHT,
            width: 18,
        },
        icon: {
            color: changeOpacity(theme.centerChannelColor, 0.56),
        },
        textContainer: {
            justifyContent: 'center',
           // flex: 1,
            height: ITEM_HEIGHT,
            marginRight: 5,
        },
        text: {
            color: theme.centerChannelColor,
            ...typography('Heading', 200),
            fontFamily:'IBMPlexSansArabic-Regular'
        },
    };
});

const SlideUpPanelItem = ({
    destructive = false,
    leftIcon,
    leftImageStyles,
    leftIconStyles,
    rightIcon,
    rightImageStyles,
    rightIconStyles,
    onPress,
    testID,
    text,
    textStyles,
}: SlideUpPanelProps) => {

 const [localLange, setChangeLang] = useState<string>(`${DEFAULT_LOCALE}`);
    const [index, setIndex] = React.useState(0);

    useEffect(() => {
        // Set the initial language based on device locale
        const locale = RNLocalize.getLocales()[0].languageCode;
        setChangeLang(locale);
        //i18n.changeLanguage(locale);
    }, []);











    const theme = useTheme();
    const style = getStyleSheet(theme);

    const { image: leftImage, iconStyle: leftIconStyle } = useImageAndStyle(leftIcon, leftImageStyles, leftIconStyles, destructive);
    const { image: rightImage, iconStyle: rightIconStyle } = useImageAndStyle(rightIcon, rightImageStyles, rightIconStyles, destructive);

    const handleOnPress = useCallback(preventDoubleTap(onPress, 500), []);

    return (
        <TouchableHighlight
            onPress={handleOnPress}
            style={style.container}
            testID={testID}
            underlayColor={changeOpacity(theme.buttonBg, 0.08)}
        >
            <View style={style.row}>
                { 
                    <View style={leftIconStyle}>{leftImage}</View>
                 }
                <View style={style.textContainer}>
                    <Text style={[style.text, destructive && style.destructive, textStyles]}>{text}</Text>
                </View>
                {/*Boolean(rightImage) &&
                    <View style={rightIconStyle}>{rightImage}</View>
                */}
                 {//localLange==='en'&&
                   // <View style={leftIconStyle}>{leftImage}</View>
                }
            </View>
        </TouchableHighlight>
    );
};

const useImageAndStyle = (icon: string | ImageSource | undefined, imageStyles: StyleProp<ImageStyle>, iconStyles: StyleProp<TextStyle>, destructive: boolean) => {
    const theme = useTheme();
    const style = getStyleSheet(theme);

    let image;
    let iconStyle: Array<StyleProp<ViewStyle>> = [style.iconContainer];
    // console.log(`\n\n\n\nthis the icon type ${typeof icon === 'object'} ${icon?.toString()}\n\n\n\n`)
    if (icon) {
        const imageStyle: StyleProp<ImageStyle> = [imageStyles];

        if (typeof icon === 'object') {

            if (icon.uri && isValidUrl(icon.uri)) {
                imageStyle.push({ width: 24, height: 24 });
                image = (
                    <Image
                        source={icon}
                        style={imageStyle}
                    />
                );
            } else {
                iconStyle = [style.noIconContainer];
            }
        }
        else if (icon === 'undefined' || icon === 'globe') {
            console.log(`\n\n\nfrom else if \n\n\n`)
            image = (
                <ChatBubbleLeftRightIcon
                size={24}
                color={style.icon.color}
                style={imageStyle}
           />)
        }

        else {
            console.log(`\n\n\nfrom else   \n\n\n`)

            const compassIconStyle = [style.icon, iconStyles];
            if (destructive) {
                compassIconStyle.push(style.destructive);
            }
            image = (
                <CompassIcon
                    name={icon}
                    size={24}
                    style={compassIconStyle}
                />
            );
        }
    }

    return { image, iconStyle };
};

export default SlideUpPanelItem;
