// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useState } from 'react';
import { FlatList, type ListRenderItemInfo, StyleSheet, View } from 'react-native';

import TeamItem from './team_item';

import type MyTeamModel from '@typings/database/models/servers/my_team';
import TeamIcon from './team_item/';
//import { TeamSelector } from '@app/screens/convert_gm_to_channel/team_selector';
import { TeamSelector } from '../../../screens/convert_gm_to_channel/team_selector';

type Props = {
    myOrderedTeams: MyTeamModel[];
    testID?: string;
    isFromHome?: boolean | null;
    curentTeam: string

}

const keyExtractor = (item: MyTeamModel) => item.id;

export default function TeamList({ myOrderedTeams, testID, isFromHome = false, curentTeam }: Props) {





    const renderTeam = ({ item: t }: ListRenderItemInfo<MyTeamModel>) => {

        return (
            <TeamItem

                myTeam={t}
            />
        );
    };
    const currentTeam = myOrderedTeams.find(x => x.id == curentTeam)
    if (isFromHome && currentTeam !== undefined)
        return <View >

            <TeamItem
                isFromHome={true}
                myTeam={currentTeam} />
        </View>
    return (
        <View style={styles.container}>
            {
                myOrderedTeams?.length > 0 &&
                <FlatList
                    bounces={false}
                    contentContainerStyle={styles.contentContainer}
                    data={myOrderedTeams}
                    fadingEdgeLength={30}
                    keyExtractor={keyExtractor}
                    renderItem={renderTeam}
                    showsVerticalScrollIndicator={false}
                    testID={`${testID}.flat_list`}
                />}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        // flexShrink: 1,
    },
    contentContainer: {
        // alignItems: 'center',
        // marginVertical: 6,
        // paddingBottom: 10,
    },
});
